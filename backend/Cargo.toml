[package]
name = "enclava_backend"
version = "0.1.0"
edition = "2024"

[dependencies]
actix-cors = "0.7.1"
actix-multipart = "0.7.2"
actix-web = "4.11.0"
chrono = { version = "0.4.41", features = ["serde"] }
color-eyre = "0.6.5"
csv = "1.3.1"
dotenvy = "0.15.7"
futures-util = "0.3.31"
toml = "0.9.3"
tracing = "0.1.41"
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
uuid = { version = "1.11.0", features = ["v4"] }
utoipa = { version = "5.4.0", features = ["actix_extras"] }
utoipa-actix-web = "0.1.2"
utoipa-swagger-ui = { version = "9.0.2", features = ["actix-web", "reqwest"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.141"
thiserror = "2.0.12"
tokio = { version = "1.46.1", features = ["full"] }
once_cell = "1.21.3"
sqlx = { version = "0.8.6", features = ["postgres", "chrono", "runtime-tokio", "runtime-tokio-rustls"] }
rig-core = { version = "0.17.1", features = ["derive"] }
dashmap = "6.1.0"
